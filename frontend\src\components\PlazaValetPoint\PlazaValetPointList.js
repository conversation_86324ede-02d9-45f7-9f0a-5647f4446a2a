import React, { useState } from 'react';
import { Search, Edit, Trash2, ToggleLeft, ToggleRight, MapPin, ChevronLeft, ChevronRight, Download } from 'lucide-react';
import { PermissionButton } from '../auth/PermissionButton';

const PlazaValetPointList = ({
  valetPoints = [],
  isLoading,
  onEdit,
  onDelete,
  onToggleStatus,
  currentPage = 1,
  itemsPerPage = 10,
  onPageChange,
  onItemsPerPageChange
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState({ key: 'ValetPointName', direction: 'ascending' });
  // Removed visibleColumns state as it's not being used in the current implementation

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6">
          <div className="animate-pulse space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (valetPoints.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-12 text-center">
          <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Plaza Valet Points</h3>
          <p className="text-gray-600 mb-4">
            Get started by creating your first plaza valet point.
          </p>
        </div>
      </div>
    );
  }

  // Filter valet points based on search term
  const filteredValetPoints = valetPoints.filter(valetPoint =>
    (valetPoint.ValetPointName && valetPoint.ValetPointName.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (valetPoint.PlazaName && valetPoint.PlazaName.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (valetPoint.PlazaCode && valetPoint.PlazaCode.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (valetPoint.CompanyName && valetPoint.CompanyName.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Sort valet points based on sortConfig
  const sortedValetPoints = [...filteredValetPoints].sort((a, b) => {
    if (a[sortConfig.key] < b[sortConfig.key]) {
      return sortConfig.direction === 'ascending' ? -1 : 1;
    }
    if (a[sortConfig.key] > b[sortConfig.key]) {
      return sortConfig.direction === 'ascending' ? 1 : -1;
    }
    return 0;
  });

  const requestSort = (key) => {
    let direction = 'ascending';
    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  const getSortIndicator = (key) => {
    if (sortConfig.key === key) {
      return sortConfig.direction === 'ascending' ? '↑' : '↓';
    }
    return '';
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatCoordinates = (lat, lng) => {
    if (!lat || !lng) return 'Not set';
    return `${parseFloat(lat).toFixed(6)}, ${parseFloat(lng).toFixed(6)}`;
  };

  // Calculate pagination
  const totalItems = sortedValetPoints.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  // Get current page items
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = sortedValetPoints.slice(indexOfFirstItem, indexOfLastItem);

  // Change page
  const handlePageChange = (pageNumber) => {
    if (pageNumber < 1 || pageNumber > totalPages) return;
    if (onPageChange) onPageChange(pageNumber);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (e) => {
    const value = parseInt(e.target.value, 10);
    if (onItemsPerPageChange) onItemsPerPageChange(value);
  };

  // Removed toggleColumnVisibility function as it's not being used

  const exportToCSV = () => {
    // Define headers for CSV export
    const headers = ['ValetPointName', 'PlazaName', 'CompanyName', 'Location', 'Status', 'CreatedOn'];

    // Create CSV header row
    let csvContent = headers.join(',') + '\n';

    // Add data rows
    sortedValetPoints.forEach(valetPoint => {
      const row = headers.map(header => {
        // Handle special cases
        if (header === 'Status') {
          return valetPoint.IsActive === true || valetPoint.IsActive === 1 || valetPoint.IsActive === '1' || valetPoint.IsActive === 'Y' ? 'Active' : 'Inactive';
        }
        if (header === 'Location') {
          return formatCoordinates(valetPoint.Latitude, valetPoint.Longitude);
        }
        if (header === 'CreatedOn') {
          return formatDate(valetPoint.CreatedOn);
        }
        return valetPoint[header] || '';
      }).join(',');
      csvContent += row + '\n';
    });

    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `plaza_valet_points_export_${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
      {/* Header with controls */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center space-x-4">
            {/* Search */}
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Search valet points..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            {/* Items per page */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-700">Show:</span>
              <select
                value={itemsPerPage}
                onChange={handleItemsPerPageChange}
                className="border border-gray-300 rounded-md px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
              </select>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {/* Export button */}
            <button
              onClick={exportToCSV}
              className="flex items-center space-x-1 bg-green-600 text-white rounded-md px-3 py-1 text-sm hover:bg-green-700"
              title="Export to CSV"
            >
              <Download size={16} />
              <span className="hidden md:inline">Export</span>
            </button>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => requestSort('ValetPointName')}
              >
                Valet Point Name {getSortIndicator('ValetPointName')}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => requestSort('PlazaName')}
              >
                Plaza {getSortIndicator('PlazaName')}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => requestSort('CompanyName')}
              >
                Company {getSortIndicator('CompanyName')}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Location
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => requestSort('IsActive')}
              >
                Status {getSortIndicator('IsActive')}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => requestSort('CreatedOn')}
              >
                Created {getSortIndicator('CreatedOn')}
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {currentItems.length === 0 ? (
              <tr>
                <td colSpan="7" className="px-6 py-4 text-center text-gray-500">
                  No valet points found. Please check your filters or try creating a new valet point.
                </td>
              </tr>
            ) : (
              currentItems.map((valetPoint) => (
                <tr
                  key={valetPoint.Id}
                  className="hover:bg-gray-50"
                >
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {valetPoint.ValetPointName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div>
                      <div className="font-medium">{valetPoint.PlazaName}</div>
                      <div className="text-xs text-gray-400">{valetPoint.PlazaCode}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {valetPoint.CompanyName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex items-center">
                      <MapPin size={14} className="mr-1 text-gray-400" />
                      <span className="text-xs">
                        {formatCoordinates(valetPoint.Latitude, valetPoint.Longitude)}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      valetPoint.IsActive === true || valetPoint.IsActive === 1 || valetPoint.IsActive === '1' || valetPoint.IsActive === 'Y'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {valetPoint.IsActive === true || valetPoint.IsActive === 1 || valetPoint.IsActive === '1' || valetPoint.IsActive === 'Y'
                        ? 'Active'
                        : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(valetPoint.CreatedOn)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex space-x-2 justify-end">
                      <PermissionButton
                        requiredModule="Valet Points"
                        requiredPermissions={["Edit"]}
                        companyId={valetPoint.CompanyId}
                        plazaId={valetPoint.PlazaId}
                        onClick={(e) => {
                          e.stopPropagation();
                          onEdit(valetPoint);
                        }}
                        className="text-blue-600 hover:text-blue-900"
                        aria-label={`Edit ${valetPoint.ValetPointName}`}
                      >
                        <Edit size={18} />
                      </PermissionButton>
                      <PermissionButton
                        requiredModule="Valet Points"
                        requiredPermissions={["Delete"]}
                        companyId={valetPoint.CompanyId}
                        plazaId={valetPoint.PlazaId}
                        onClick={(e) => {
                          e.stopPropagation();
                          onDelete(valetPoint.Id);
                        }}
                        className="text-red-600 hover:text-red-900"
                        aria-label={`Delete ${valetPoint.ValetPointName}`}
                      >
                        <Trash2 size={18} />
                      </PermissionButton>

                      {onToggleStatus && (
                        <PermissionButton
                          requiredModule="Valet Points"
                          requiredPermissions={["Edit"]}
                          companyId={valetPoint.CompanyId}
                          plazaId={valetPoint.PlazaId}
                          onClick={(e) => {
                            e.stopPropagation();
                            onToggleStatus(valetPoint.Id, valetPoint.IsActive);
                          }}
                          className={`${
                            valetPoint.IsActive === true || valetPoint.IsActive === 1 || valetPoint.IsActive === '1' || valetPoint.IsActive === 'Y'
                              ? 'text-green-600 hover:text-green-900'
                              : 'text-gray-600 hover:text-gray-900'
                          }`}
                          aria-label={`${
                            valetPoint.IsActive === true || valetPoint.IsActive === 1 || valetPoint.IsActive === '1' || valetPoint.IsActive === 'Y'
                              ? 'Deactivate'
                              : 'Activate'
                          } ${valetPoint.ValetPointName}`}
                        >
                          {valetPoint.IsActive === true || valetPoint.IsActive === 1 || valetPoint.IsActive === '1' || valetPoint.IsActive === 'Y'
                            ? <ToggleRight size={18} />
                            : <ToggleLeft size={18} />}
                        </PermissionButton>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-4 py-3 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing {indexOfFirstItem + 1} to {Math.min(indexOfLastItem, totalItems)} of {totalItems} results
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={() => handlePageChange(1)}
                disabled={currentPage === 1}
                className={`p-2 rounded-md ${
                  currentPage === 1
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-gray-700 hover:bg-gray-200'
                }`}
              >
                <span className="sr-only">First Page</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                </svg>
              </button>

              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className={`p-2 rounded-md ${
                  currentPage === 1
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-gray-700 hover:bg-gray-200'
                }`}
              >
                <span className="sr-only">Previous Page</span>
                <ChevronLeft className="h-5 w-5" />
              </button>

              <div className="flex items-center">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  // Show pages around current page
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }

                  return (
                    <button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`px-3 py-1 rounded-md mx-1 ${
                        currentPage === pageNum
                          ? 'bg-blue-600 text-white'
                          : 'text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
              </div>

              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className={`p-2 rounded-md ${
                  currentPage === totalPages
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-gray-700 hover:bg-gray-200'
                }`}
              >
                <span className="sr-only">Next Page</span>
                <ChevronRight className="h-5 w-5" />
              </button>

              <button
                onClick={() => handlePageChange(totalPages)}
                disabled={currentPage === totalPages}
                className={`p-2 rounded-md ${
                  currentPage === totalPages
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-gray-700 hover:bg-gray-200'
                }`}
              >
                <span className="sr-only">Last Page</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  <path fillRule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PlazaValetPointList;
