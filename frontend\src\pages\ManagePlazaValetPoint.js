import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus, Filter } from 'lucide-react';
import { useToast } from '../hooks/useToast';
import { plazaValetPointApi } from '../api/plazaValetPointApi';
import { companyApi } from '../api/companyApi';
import { plazaApi } from '../api/plazaApi';
import PlazaValetPointList from '../components/PlazaValetPoint/PlazaValetPointList';
import PlazaValetPointDialog from '../components/PlazaValetPoint/PlazaValetPointDialog';
import { useAuth } from '../contexts/authContext';
import useModuleFilter from '../hooks/useModuleFilter';
import { PermissionButton } from '../components/auth/PermissionButton';

const ManagePlazaValetPoint = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingValetPoint, setEditingValetPoint] = useState(null);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const queryClient = useQueryClient();
  const toast = useToast();
  const { user } = useAuth();

  // State for selected company and plaza in filters
  const [selectedFilterCompany, setSelectedFilterCompany] = useState('');
  const [selectedFilterPlaza, setSelectedFilterPlaza] = useState('');

  // Fetch companies for dropdown
  const { data: companies = [] } = useQuery({
    queryKey: ['companies'],
    queryFn: companyApi.getCompanies,
    select: (data) => Array.isArray(data) ? data : data?.companies || []
  });

  // Fetch plazas based on selected company
  const { data: plazas = [], isLoading: plazasLoading, error: plazasError } = useQuery({
    queryKey: ['plazas', selectedFilterCompany],
    queryFn: async () => {
      try {
        console.log(`Fetching plazas for company filter: ${selectedFilterCompany}`);
        const data = selectedFilterCompany
          ? await plazaApi.getPlazasByCompany(selectedFilterCompany)
          : await plazaApi.getAllPlazas();
        console.log('Plazas data received for filter:', data);
        return data;
      } catch (error) {
        console.error('Error fetching plazas for filter:', error);
        throw error;
      }
    },
    select: (data) => {
      console.log('Processing plazas data for select:', data);

      // Handle different response structures
      if (!data) return [];

      if (Array.isArray(data)) {
        return data;
      } else if (data.data && Array.isArray(data.data)) {
        return data.data;
      } else if (data.plazas && Array.isArray(data.plazas)) {
        return data.plazas;
      }

      console.warn('Unexpected plazas data structure:', data);
      return [];
    }
  });

  // Fetch plaza valet points
  const { data: valetPoints = [], isLoading, error } = useQuery({
    queryKey: ['plazavaletpoints'],
    queryFn: plazaValetPointApi.getAllPlazaValetPoints,
    onError: (error) => {
      console.error('Error fetching plaza valet points:', error);
      toast.showError('Failed to fetch plaza valet points');
    },
    select: (data) => data?.valetPoints || []
  });

  // Use the module filter hook for role-based filtering
  const {
    filteredData: filteredValetPoints,
    filters,
    setFilters
  } = useModuleFilter({
    data: valetPoints,
    companies,
    plazas,
    companyIdField: 'CompanyId',
    plazaIdField: 'PlazaId',
    module: 'PlazaValetPoints'
  });

  // Create mutation
  const createMutation = useMutation({
    mutationFn: plazaValetPointApi.createPlazaValetPoint,
    onSuccess: () => {
      queryClient.invalidateQueries(['plazavaletpoints']);
      toast.showCrudSuccess('create', 'Plaza valet point');
      setIsDialogOpen(false);
      setEditingValetPoint(null);
    },
    onError: (error) => {
      console.error('Error creating plaza valet point:', error);
      toast.showError(error.response?.data?.message || 'Failed to create plaza valet point');
    }
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }) => plazaValetPointApi.updatePlazaValetPoint(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries(['plazavaletpoints']);
      toast.showCrudSuccess('update', 'Plaza valet point');
      setIsDialogOpen(false);
      setEditingValetPoint(null);
    },
    onError: (error) => {
      console.error('Error updating plaza valet point:', error);
      toast.showError(error.response?.data?.message || 'Failed to update plaza valet point');
    }
  });

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: plazaValetPointApi.deletePlazaValetPoint,
    onSuccess: () => {
      queryClient.invalidateQueries(['plazavaletpoints']);
      toast.showCrudSuccess('delete', 'Plaza valet point');
    },
    onError: (error) => {
      console.error('Error deleting plaza valet point:', error);
      toast.showError(error.response?.data?.message || 'Failed to delete plaza valet point');
    }
  });

  // Toggle status mutation
  const toggleStatusMutation = useMutation({
    mutationFn: plazaValetPointApi.toggleActiveStatus,
    onSuccess: (data) => {
      queryClient.invalidateQueries(['plazavaletpoints']);
      toast.showSuccess(`Plaza valet point ${data.isActive ? 'activated' : 'deactivated'} successfully`);
    },
    onError: (error) => {
      console.error('Error toggling plaza valet point status:', error);
      toast.showError(error.response?.data?.message || 'Failed to update plaza valet point status');
    }
  });

  const handleCreate = () => {
    setEditingValetPoint(null);
    setIsDialogOpen(true);
  };

  const handleEdit = (valetPoint) => {
    setEditingValetPoint(valetPoint);
    setIsDialogOpen(true);
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this plaza valet point?')) {
      deleteMutation.mutate(id);
    }
  };

  const handleToggleStatus = (id, currentStatus) => {
    const action = currentStatus ? 'deactivate' : 'activate';
    if (window.confirm(`Are you sure you want to ${action} this plaza valet point?`)) {
      toggleStatusMutation.mutate(id);
    }
  };

  const handleSubmit = (data) => {
    if (editingValetPoint) {
      updateMutation.mutate({ id: editingValetPoint.Id, data });
    } else {
      createMutation.mutate(data);
    }
  };

  // Handle company filter change
  const handleCompanyFilterChange = (e) => {
    const companyId = e.target.value;
    setSelectedFilterCompany(companyId);
    setSelectedFilterPlaza(''); // Reset plaza when company changes
    setFilters({
      ...filters,
      companyId: companyId,
      plazaId: '' // Reset plaza filter
    });
    setCurrentPage(1); // Reset to first page when filter changes
  };

  // Handle plaza filter change
  const handlePlazaFilterChange = (e) => {
    const plazaId = e.target.value;
    setSelectedFilterPlaza(plazaId);
    setFilters({
      ...filters,
      plazaId: plazaId
    });
    setCurrentPage(1); // Reset to first page when filter changes
  };

  // Handle page change
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value) => {
    setItemsPerPage(value);
    setCurrentPage(1); // Reset to first page when items per page changes
  };

  const clearFilters = () => {
    setFilters({});
    setSelectedFilterCompany('');
    setSelectedFilterPlaza('');
    setCurrentPage(1); // Reset to first page when filters are cleared
  };

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [filters]);

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-red-800 font-medium">Error Loading Plaza Valet Points</h3>
          <p className="text-red-600 mt-1">
            {error.response?.data?.message || 'Failed to load plaza valet points. Please try again.'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Plaza Valet Point Management</h1>
            <p className="text-gray-600 mt-1">
              Manage valet parking points for your plazas
            </p>
          </div>

          <div className="flex items-center gap-3">
            <button
              onClick={() => setIsFilterOpen(!isFilterOpen)}
              className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Filter className="w-4 h-4" />
              <span>Filters</span>
            </button>

            <PermissionButton
              requiredModule="Valet Points"
              requiredPermissions={["Create"]}
              onClick={handleCreate}
              className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span>Add Valet Point</span>
            </PermissionButton>
          </div>
        </div>

        {/* Filters */}
        {isFilterOpen && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg border">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Company Filter - Only show for SuperAdmin */}
              {user?.role === 'SuperAdmin' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Company
                  </label>
                  <select
                    value={selectedFilterCompany}
                    onChange={handleCompanyFilterChange}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">All Companies</option>
                    {companies.map((company) => (
                      <option key={company.Id} value={company.Id}>
                        {company.CompanyName}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* Plaza Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Plaza
                </label>
                <select
                  value={selectedFilterPlaza}
                  onChange={handlePlazaFilterChange}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  disabled={user?.role === 'SuperAdmin' && !selectedFilterCompany}
                >
                  {user?.role === 'SuperAdmin' && !selectedFilterCompany ? (
                    <option value="">Select a company first</option>
                  ) : plazasLoading ? (
                    <option value="">Loading plazas...</option>
                  ) : plazasError ? (
                    <option value="">Error loading plazas</option>
                  ) : (
                    <>
                      <option value="">All Plazas</option>
                      {Array.isArray(plazas) && plazas.length > 0 ? plazas.map((plaza) => {
                        const id = plaza.Id || plaza.id;
                        const name = plaza.PlazaName || plaza.plazaName || plaza.name;

                        return (
                          <option key={id} value={id}>
                            {name}
                          </option>
                        );
                      }) : (
                        <option value="" disabled>No plazas available</option>
                      )}
                    </>
                  )}
                </select>
                {Array.isArray(plazas) && plazas.length === 0 && selectedFilterCompany && !plazasLoading && (
                  <p className="text-sm text-orange-500 mt-1">No plazas found for this company</p>
                )}
              </div>
            </div>

            <div className="mt-4 flex justify-end">
              <button
                onClick={clearFilters}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Clear Filters
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Plaza Valet Point List */}
      <PlazaValetPointList
        valetPoints={filteredValetPoints}
        isLoading={isLoading}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onToggleStatus={handleToggleStatus}
        currentPage={currentPage}
        itemsPerPage={itemsPerPage}
        onPageChange={handlePageChange}
        onItemsPerPageChange={handleItemsPerPageChange}
      />

      {/* Plaza Valet Point Dialog */}
      {isDialogOpen && (
        <PlazaValetPointDialog
          isOpen={isDialogOpen}
          onClose={() => {
            setIsDialogOpen(false);
            setEditingValetPoint(null);
          }}
          onSubmit={handleSubmit}
          initialData={editingValetPoint}
          title={editingValetPoint ? 'Edit Plaza Valet Point' : 'Create Plaza Valet Point'}
          companies={companies}
          plazas={plazas}
          isLoading={createMutation.isLoading || updateMutation.isLoading}
        />
      )}
    </div>
  );
};

export default ManagePlazaValetPoint;
