import React, { useState, useMemo } from 'react';
import { 
  CheckSquare, 
  Square, 
  ChevronDown, 
  ChevronRight, 
  Users, 
  Shield,
  Eye,
  Plus,
  Edit,
  Trash2,
  Download,
  Upload,
  Settings
} from 'lucide-react';

const PermissionMatrix = ({
  modules,
  roles,
  matrix,
  searchTerm,
  filterModule,
  onPermissionUpdate,
  onBulkUpdate,
  onRoleSelect,
  isLoading
}) => {
  const [expandedModules, setExpandedModules] = useState(new Set());
  const [selectedCells, setSelectedCells] = useState(new Set());

  // Permission icons mapping
  const permissionIcons = {
    'View': Eye,
    'Create': Plus,
    'Edit': Edit,
    'Delete': Trash2,
    'Export': Download,
    'Import': Upload,
    'Approve': CheckSquare
  };

  // Filter and search logic
  const filteredModules = useMemo(() => {
    let filtered = modules;

    // Filter by module if selected
    if (filterModule) {
      filtered = filtered.filter(module => module.id.toString() === filterModule);
    }

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(module => 
        module.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        module.subModules.some(sub => 
          sub.name.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }

    return filtered;
  }, [modules, filterModule, searchTerm]);

  // Create permission matrix data structure
  const matrixData = useMemo(() => {
    const data = {};
    
    // Initialize structure
    roles.forEach(role => {
      data[role.Id] = {};
    });

    // Populate with matrix data
    matrix.forEach(item => {
      if (!data[item.RoleId]) {
        data[item.RoleId] = {};
      }
      
      const key = `${item.ModuleId}-${item.SubModuleId}-${item.PermissionId}`;
      data[item.RoleId][key] = {
        hasPermission: item.HasPermission === 1,
        subModulePermissionId: item.SubModulePermissionId
      };
    });

    return data;
  }, [roles, matrix]);

  // Toggle module expansion
  const toggleModule = (moduleId) => {
    const newExpanded = new Set(expandedModules);
    if (newExpanded.has(moduleId)) {
      newExpanded.delete(moduleId);
    } else {
      newExpanded.add(moduleId);
    }
    setExpandedModules(newExpanded);
  };

  // Handle permission toggle
  const handlePermissionToggle = (roleId, subModulePermissionId, currentValue) => {
    const permissions = [{
      subModulePermissionId: subModulePermissionId,
      isActive: !currentValue
    }];
    
    onPermissionUpdate(roleId, permissions);
  };

  // Handle bulk operations
  const handleBulkToggle = (roleId, moduleId, subModuleId, permissionType, newValue) => {
    const module = filteredModules.find(m => m.id === moduleId);
    if (!module) return;

    let updates = [];

    if (subModuleId) {
      // Toggle all permissions for a specific submodule
      const subModule = module.subModules.find(sm => sm.id === subModuleId);
      if (subModule) {
        updates = subModule.permissions.map(perm => ({
          subModulePermissionId: perm.subModulePermissionId,
          isActive: newValue
        }));
      }
    } else {
      // Toggle all permissions for entire module
      module.subModules.forEach(subModule => {
        subModule.permissions.forEach(perm => {
          if (!permissionType || perm.name === permissionType) {
            updates.push({
              subModulePermissionId: perm.subModulePermissionId,
              isActive: newValue
            });
          }
        });
      });
    }

    if (updates.length > 0) {
      onBulkUpdate(roleId, updates);
    }
  };

  // Get permission status
  const getPermissionStatus = (roleId, moduleId, subModuleId, permissionId) => {
    const key = `${moduleId}-${subModuleId}-${permissionId}`;
    return matrixData[roleId]?.[key] || { hasPermission: false, subModulePermissionId: null };
  };

  return (
    <div className="overflow-hidden">
      {/* Header */}
      <div className="bg-gray-50 border-b p-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Shield className="w-5 h-5 text-blue-600" />
            Permission Matrix
          </h3>
          <div className="text-sm text-gray-600">
            {filteredModules.length} modules • {roles.length} roles
          </div>
        </div>
      </div>

      {/* Matrix Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          {/* Table Header */}
          <thead className="bg-gray-50 border-b sticky top-0 z-10">
            <tr>
              <th className="text-left p-4 font-semibold text-gray-900 min-w-[300px]">
                Modules & Features
              </th>
              {roles.map(role => (
                <th key={role.Id} className="text-center p-4 font-semibold text-gray-900 min-w-[120px]">
                  <button
                    onClick={() => onRoleSelect(role)}
                    className="flex flex-col items-center gap-1 hover:text-blue-600 transition-colors"
                  >
                    <Users className="w-4 h-4" />
                    <span className="text-xs">{role.Name}</span>
                  </button>
                </th>
              ))}
            </tr>
          </thead>

          {/* Table Body */}
          <tbody>
            {filteredModules.map(module => (
              <React.Fragment key={module.id}>
                {/* Module Row */}
                <tr className="border-b bg-blue-50 hover:bg-blue-100">
                  <td className="p-4">
                    <button
                      onClick={() => toggleModule(module.id)}
                      className="flex items-center gap-2 text-left w-full font-semibold text-blue-900 hover:text-blue-700"
                    >
                      {expandedModules.has(module.id) ? (
                        <ChevronDown className="w-4 h-4" />
                      ) : (
                        <ChevronRight className="w-4 h-4" />
                      )}
                      <span className="text-lg">{module.name}</span>
                      <span className="text-sm text-blue-600 ml-2">
                        ({module.subModules.length} features)
                      </span>
                    </button>
                  </td>
                  {roles.map(role => (
                    <td key={role.Id} className="p-4 text-center">
                      <button
                        onClick={() => handleBulkToggle(role.Id, module.id, null, null, true)}
                        className="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
                        disabled={isLoading}
                      >
                        Grant All
                      </button>
                    </td>
                  ))}
                </tr>

                {/* SubModule Rows */}
                {expandedModules.has(module.id) && module.subModules.map(subModule => (
                  <React.Fragment key={subModule.id}>
                    {/* SubModule Header */}
                    <tr className="border-b bg-gray-50">
                      <td className="p-4 pl-8">
                        <div className="font-medium text-gray-900">
                          {subModule.name}
                        </div>
                        <div className="text-sm text-gray-600">
                          {subModule.permissions.length} permissions
                        </div>
                      </td>
                      {roles.map(role => (
                        <td key={role.Id} className="p-4 text-center">
                          <button
                            onClick={() => handleBulkToggle(role.Id, module.id, subModule.id, null, true)}
                            className="text-xs bg-gray-600 text-white px-2 py-1 rounded hover:bg-gray-700"
                            disabled={isLoading}
                          >
                            Grant
                          </button>
                        </td>
                      ))}
                    </tr>

                    {/* Permission Rows */}
                    {subModule.permissions.map(permission => {
                      const PermissionIcon = permissionIcons[permission.name] || Settings;
                      
                      return (
                        <tr key={permission.id} className="border-b hover:bg-gray-50">
                          <td className="p-4 pl-12">
                            <div className="flex items-center gap-2">
                              <PermissionIcon className="w-4 h-4 text-gray-500" />
                              <span className="text-gray-900">{permission.name}</span>
                              {permission.description && (
                                <span className="text-sm text-gray-500">
                                  - {permission.description}
                                </span>
                              )}
                            </div>
                          </td>
                          {roles.map(role => {
                            const status = getPermissionStatus(
                              role.Id, 
                              module.id, 
                              subModule.id, 
                              permission.id
                            );
                            
                            return (
                              <td key={role.Id} className="p-4 text-center">
                                <button
                                  onClick={() => handlePermissionToggle(
                                    role.Id,
                                    status.subModulePermissionId,
                                    status.hasPermission
                                  )}
                                  disabled={isLoading || !status.subModulePermissionId}
                                  className={`p-1 rounded transition-colors ${
                                    status.hasPermission
                                      ? 'text-green-600 hover:text-green-700'
                                      : 'text-gray-400 hover:text-gray-600'
                                  } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                                >
                                  {status.hasPermission ? (
                                    <CheckSquare className="w-5 h-5" />
                                  ) : (
                                    <Square className="w-5 h-5" />
                                  )}
                                </button>
                              </td>
                            );
                          })}
                        </tr>
                      );
                    })}
                  </React.Fragment>
                ))}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>

      {/* Footer */}
      <div className="bg-gray-50 border-t p-4">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <CheckSquare className="w-4 h-4 text-green-600" />
              <span>Granted</span>
            </div>
            <div className="flex items-center gap-2">
              <Square className="w-4 h-4 text-gray-400" />
              <span>Denied</span>
            </div>
          </div>
          <div>
            Click on role names to edit individual role permissions
          </div>
        </div>
      </div>
    </div>
  );
};

export default PermissionMatrix;
