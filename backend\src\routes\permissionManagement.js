const express = require('express');
const router = express.Router();
const permissionManagementController = require('../controllers/PermissionManagementController');
const authMiddleware = require('../middleware/auth');

// Test endpoint without auth for debugging
router.get('/test', (req, res) => {
  res.json({ success: true, message: 'Permission management routes working' });
});

// Apply authentication middleware to all routes
router.use(authMiddleware);

/**
 * @route GET /api/permission-management/modules-tree
 * @desc Get complete modules tree with submodules and permissions
 * @access SuperAdmin only
 */
router.get('/modules-tree', (req, res, next) => {
  // Only SuperAdmin can access permission management
  if (req.user.role !== 'SuperAdmin') {
    return res.status(403).json({
      success: false,
      message: 'Access denied. Only SuperAdmin can manage permissions.'
    });
  }
  next();
}, permissionManagementController.getModulesTree);

/**
 * @route GET /api/permission-management/roles
 * @desc Get all roles
 * @access SuperAdmin only
 */
router.get('/roles', (req, res, next) => {
  if (req.user.role !== 'SuperAdmin') {
    return res.status(403).json({
      success: false,
      message: 'Access denied. Only SuperAdmin can manage permissions.'
    });
  }
  next();
}, permissionManagementController.getRoles);

/**
 * @route GET /api/permission-management/roles/:roleId/permissions
 * @desc Get permissions for a specific role
 * @access SuperAdmin only
 */
router.get('/roles/:roleId/permissions', (req, res, next) => {
  if (req.user.role !== 'SuperAdmin') {
    return res.status(403).json({
      success: false,
      message: 'Access denied. Only SuperAdmin can manage permissions.'
    });
  }
  next();
}, permissionManagementController.getRolePermissions);

/**
 * @route PUT /api/permission-management/roles/:roleId/permissions
 * @desc Update permissions for a specific role
 * @access SuperAdmin only
 */
router.put('/roles/:roleId/permissions', (req, res, next) => {
  if (req.user.role !== 'SuperAdmin') {
    return res.status(403).json({
      success: false,
      message: 'Access denied. Only SuperAdmin can manage permissions.'
    });
  }
  next();
}, permissionManagementController.updateRolePermissions);

/**
 * @route GET /api/permission-management/matrix
 * @desc Get permission matrix for all roles
 * @access SuperAdmin only
 */
router.get('/matrix', (req, res, next) => {
  if (req.user.role !== 'SuperAdmin') {
    return res.status(403).json({
      success: false,
      message: 'Access denied. Only SuperAdmin can manage permissions.'
    });
  }
  next();
}, permissionManagementController.getPermissionMatrix);

module.exports = router;
